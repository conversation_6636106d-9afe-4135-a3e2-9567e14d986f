'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '../context/AuthContext';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';

interface RouteGuardProps {
  children: React.ReactNode;
}

export default function RouteGuard({ children }: RouteGuardProps) {
  const { user, loading, isAuthenticated } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isChecking, setIsChecking] = useState(true);

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/auth/callback',
    '/auth/zerodha/callback',
    '/auth/zerodha/child-callback',
    '/auth/accept-invitation',
    '/demo',
  ];

  // Check if the current path is a public route
  const isPublicRoute = publicRoutes.some(route => {
    if (route === '/') {
      return pathname === '/';
    }
    if (route === '/demo') {
      return pathname.startsWith('/demo');
    }
    return pathname.startsWith(route);
  });

  useEffect(() => {
    console.log('RouteGuard - loading:', loading, 'isAuthenticated:', isAuthenticated, 'user:', user, 'pathname:', pathname, 'isPublicRoute:', isPublicRoute);

    // Wait for auth context to finish loading
    if (loading) {
      console.log('Still loading, waiting...');
      return;
    }

    // If it's a public route, allow access
    if (isPublicRoute) {
      console.log('Public route, allowing access');
      setIsChecking(false);
      return;
    }

    // For protected routes, check authentication
    if (!isAuthenticated) {
      console.log('User not authenticated, redirecting to home');
      // TEMPORARILY DISABLED FOR DEBUGGING
      // router.push('/');
      // return;
    }

    // User is authenticated, allow access
    console.log('User authenticated, allowing access to protected route');
    setIsChecking(false);
  }, [loading, isAuthenticated, user, isPublicRoute, pathname, router]);

  // Show loading spinner while checking authentication
  if (loading || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="flex flex-col items-center space-y-4"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <Loader2 className="w-8 h-8 text-primary" />
          </motion.div>
          <p className="text-muted-foreground">
            {loading ? "Checking authentication..." : "Completing authentication..."}
          </p>
        </motion.div>
      </div>
    );
  }

  // If user is not authenticated and trying to access protected route, don't render children
  if (!isPublicRoute && !isAuthenticated) {
    return null;
  }

  // Render children for public routes or authenticated users
  return <>{children}</>;
}
