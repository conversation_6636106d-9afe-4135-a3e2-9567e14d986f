'use client';

import { useAuth } from '../context/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertCircle, X } from 'lucide-react';
import HeroSection from './landing/HeroSection';
import FeaturesSection from './landing/FeaturesSection';
import TestimonialsSection from './landing/TestimonialsSection';
import PricingSection from './landing/PricingSection';
import FAQSection from './landing/FAQSection';
import Footer from './landing/Footer';

export default function LandingPage() {
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showAuthAlert, setShowAuthAlert] = useState(false);
  const [redirectPath, setRedirectPath] = useState<string | null>(null);

  useEffect(() => {
    // Check if user was redirected due to authentication issues
    const redirect = searchParams.get('redirect');
    if (redirect) {
      setRedirectPath(redirect);
      setShowAuthAlert(true);

      // Clean up URL by removing the redirect parameter
      const url = new URL(window.location.href);
      url.searchParams.delete('redirect');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams]);

  const handleGetStarted = () => {
    if (isAuthenticated) {
      // If user was redirected from a specific path, go back there
      if (redirectPath) {
        router.push(redirectPath);
        setRedirectPath(null);
        setShowAuthAlert(false);
      } else {
        router.push(user?.role === 'master' ? '/master/dashboard' : '/child/dashboard');
      }
    } else {
      router.push('/auth/register');
    }
  };

  const handleLoginAfterRedirect = () => {
    // This will trigger the login modal or redirect to auth
    handleGetStarted();
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Authentication Alert */}
      <AnimatePresence>
        {showAuthAlert && (
          <motion.div
            initial={{ opacity: 0, y: -100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -100 }}
            transition={{ duration: 0.3 }}
            className="fixed top-20 left-0 right-0 z-50 mx-4"
          >
            <div className="max-w-4xl mx-auto">
              <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 shadow-lg">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                        Authentication Required
                      </h3>
                      <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
                        You need to log in to access {redirectPath}. Please sign in to continue.
                      </p>
                      <button
                        onClick={handleLoginAfterRedirect}
                        className="mt-2 text-sm font-medium text-amber-800 dark:text-amber-200 hover:text-amber-900 dark:hover:text-amber-100 underline"
                      >
                        Sign in now
                      </button>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowAuthAlert(false)}
                    className="text-amber-600 dark:text-amber-400 hover:text-amber-800 dark:hover:text-amber-200"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <HeroSection
        isAuthenticated={isAuthenticated}
        user={user}
        onGetStarted={handleGetStarted}
      />
      <div id="features">
        <FeaturesSection />
      </div>
      <TestimonialsSection />
      <div id="pricing">
        <PricingSection />
      </div>
      <div id="faq">
        <FAQSection />
      </div>
      <Footer />
    </div>
  );
}
